<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="Current" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup />
  <ItemGroup>
    <EmbeddedResource Update="SharedResource.de.resx">
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Update="SharedResource.en.resx">
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Update="SharedResource.fr.resx">
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Update="SharedResource.it.resx">
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Update="SharedResource.resx">
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Update="TermsAndConditions\TermsAndConditions.de.resx">
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Update="TermsAndConditions\TermsAndConditions.en.resx">
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Update="TermsAndConditions\TermsAndConditions.fr.resx">
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Update="TermsAndConditions\TermsAndConditions.it.resx">
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Update="TermsAndConditions\TermsAndConditions.resx">
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Update="PrivacyPolicy\PrivacyPolicy.de.resx">
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Update="PrivacyPolicy\PrivacyPolicy.en.resx">
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Update="PrivacyPolicy\PrivacyPolicy.fr.resx">
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Update="PrivacyPolicy\PrivacyPolicy.it.resx">
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Update="PrivacyPolicy\PrivacyPolicy.resx">
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
</Project>